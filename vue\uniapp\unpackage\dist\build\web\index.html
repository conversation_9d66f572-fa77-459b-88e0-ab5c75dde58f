<!DOCTYPE html>
<html lang="en">
  <head>
    <link rel="stylesheet" href="/assets/uni.ed0a7455.css">

    <meta charset="UTF-8" />
	<meta name="mobile-web-app-capable" content="yes"> <!-- 启用应用模式 -->
	<meta name="apple-mobile-web-app-title" content="悦心花"> <!-- 主屏幕显示名称 -->
	<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"> <!-- 状态栏样式 -->
	<meta name="theme-color" content="#1a6eff">
	<!-- 禁止Safari自动猜测启动地址 -->
	<meta name="apple-mobile-web-app-capable" content="yes">
	<meta name="apple-mobile-web-app-start-url" content="/#/">
	<link rel="apple-touch-icon" href="/assets/icon-128x128-Bpao4R-b.png">
	<link rel="manifest" href="/assets/pwaManifest-Bw3AFJZR.json">
	
    <script>
      var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
        CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
        (coverSupport ? ', viewport-fit=cover' : '') + '" />')
    </script>
    <title>悦心花</title>
    <!--preload-links-->
    <!--app-context-->
    <script type="module" crossorigin src="/assets/index-CZqU7lGv.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-C7HCB_Il.css">
  </head>
  <body>
    <div id="app"><!--app-html--></div>

  </body>
</html>
